# 公告系统完善文档

## 📋 问题分析

通过分析项目代码，发现公告数据结构存在以下问题：

1. **数据结构不一致**：
   - 不同文件中使用了不同的字段名（`isVisible` vs `isSticky`）
   - 缺少统一的数据结构定义

2. **缺少重要字段**：
   - 缺少 `isSticky`（是否置顶）字段
   - 缺少 `priority`（优先级）字段的统一处理

3. **后台管理功能不完整**：
   - 表单缺少置顶、优先级等字段
   - 删除操作是软删除，需要改为硬删除

## 🔧 修复内容

### 1. 统一公告数据结构

**正确的数据结构**：
```javascript
{
  _id: ObjectId,              // 自动生成的ID
  title: String,              // 公告标题
  content: String,            // Markdown格式内容
  type: String,               // 类型：'announcement', 'update', 'notice', 'maintenance'
  isSticky: Boolean,          // 是否置顶
  priority: Number,           // 优先级 (1-10, 数字越大优先级越高)
  status: String,             // 状态：'draft', 'published', 'archived'
  publishTime: Date,          // 发布时间
  expiryTime: Date,           // 过期时间（可选，null表示不过期）
  viewCount: Number,          // 查看次数
  createdAt: Date,            // 创建时间
  updatedAt: Date             // 更新时间
}
```

### 2. 修改的文件

#### 云函数数据库层
- ✅ `cloudfunctions/cloud-functions/db/announcements.js`
  - 更新 `createAnnouncement` 方法，添加 `isSticky`, `priority` 字段
  - 统一字段命名

- ✅ `cloudfunctions/cloud-functions-admin/db/announcements.js`
  - 更新 `createAnnouncement` 方法，统一数据结构
  - 使用 `isSticky` 替代 `isVisible`

#### 云函数API层
- ✅ `cloudfunctions/cloud-functions-admin/api/announcements-admin.js`
  - 更新 `createAnnouncement` API，支持 `isSticky`, `priority` 参数
  - 更新 `updateAnnouncement` API，支持新字段
  - 修改 `deleteAnnouncement` API，改为硬删除
  - 添加优先级验证（1-10范围）

#### 后台管理前端
- ✅ `admin-app/src/views/Announcements/index.vue`
  - 表单添加"是否置顶"开关
  - 表单添加"优先级"选择器（1-10）
  - 表格添加置顶和优先级列显示
  - 修改删除确认对话框，明确是永久删除
  - 更新表单数据结构和重置函数

- ✅ `admin-app/src/api/announcements.js`
  - 移除删除API的软删除参数
  - 更新API注释

#### 文档更新
- ✅ `cloudfunctions/README.md`
  - 更新公告数据结构说明
  - 统一字段定义和注释

## 🎯 功能改进

### 1. 新增字段功能

**是否置顶 (isSticky)**：
- 类型：Boolean
- 默认值：false
- 功能：置顶公告会优先显示
- 前端显示：表格中显示📌图标

**优先级 (priority)**：
- 类型：Number (1-10)
- 默认值：5
- 功能：数字越大优先级越高
- 前端显示：不同颜色的标签
  - 8-10：红色（高优先级）
  - 6-7：橙色（中优先级）
  - 1-5：蓝色（低优先级）

### 2. 删除操作改进

**硬删除**：
- 移除软删除逻辑
- 直接物理删除记录
- 更新确认对话框文案
- 明确提示"此操作不可恢复"

### 3. 表单验证

**新增验证规则**：
- 优先级必须在1-10之间
- 公告类型限制为：announcement, notice, update, maintenance
- 标题不超过100字符
- 内容不超过5000字符

## 🔄 数据迁移

由于这是开发中的新项目，不需要数据迁移。新的数据结构将直接应用于新创建的公告。

## ✅ 验证清单

- [x] 云函数数据库层统一字段定义
- [x] 云函数API层支持新字段
- [x] 删除操作改为硬删除
- [x] 后台管理表单添加新字段
- [x] 表格显示新字段
- [x] 删除确认对话框更新
- [x] API参数验证
- [x] 文档更新

## 🚀 部署说明

### 1. 云函数部署
```bash
# 部署用户端云函数
cd cloudfunctions/cloud-functions
npm run deploy

# 部署管理端云函数
cd cloudfunctions/cloud-functions-admin
npm run deploy
```

### 2. 后台管理应用
```bash
cd admin-app
pnpm install
pnpm tauri dev  # 开发模式
pnpm tauri build  # 构建应用
```

## 📝 使用说明

### 创建公告
1. 在后台管理系统中点击"创建公告"
2. 填写标题、内容（支持Markdown）
3. 选择公告类型
4. 设置是否置顶
5. 选择优先级（1-10）
6. 设置发布时间和过期时间（可选）
7. 选择状态（草稿/已发布）

### 管理公告
- **查看**：点击查看按钮预览公告内容
- **编辑**：点击编辑按钮修改公告信息
- **删除**：点击删除按钮永久删除公告（不可恢复）
- **批量操作**：选择多个公告进行批量删除

### 公告显示规则
1. 置顶公告优先显示
2. 按优先级排序（高优先级在前）
3. 同优先级按发布时间倒序
4. 过期公告自动隐藏

现在公告系统已经完善，支持置顶、优先级管理，并改为硬删除操作。
