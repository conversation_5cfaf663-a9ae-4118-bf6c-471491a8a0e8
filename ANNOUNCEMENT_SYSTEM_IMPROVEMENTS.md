# 公告系统完善文档

## 📋 问题分析

通过分析项目代码，发现公告数据结构存在以下问题：

1. **数据结构不一致**：
   - 不同文件中使用了不同的字段名（`isVisible` vs `isSticky`）
   - 缺少统一的数据结构定义

2. **缺少重要字段**：
   - 缺少 `isSticky`（是否置顶）字段
   - 缺少 `priority`（优先级）字段的统一处理

3. **后台管理功能不完整**：
   - 表单缺少置顶、优先级等字段
   - 删除操作是软删除，需要改为硬删除

## 🔧 修复内容

### 1. 统一公告数据结构

**正确的数据结构**：
```javascript
{
  _id: ObjectId,              // 自动生成的ID
  title: String,              // 公告标题
  content: String,            // Markdown格式内容
  type: String,               // 类型：'announcement', 'update', 'notice', 'maintenance'
  isSticky: Boolean,          // 是否置顶
  status: String,             // 状态：'draft', 'published', 'archived'
  publishTime: Date,          // 发布时间
  expiryTime: Date,           // 过期时间（可选，null表示不过期）
  createdAt: Date,            // 创建时间
  updatedAt: Date             // 更新时间
}
```

### 2. 修改的文件

#### 云函数数据库层
- ✅ `cloudfunctions/cloud-functions/db/announcements.js`
  - 更新 `createAnnouncement` 方法，添加 `isSticky`, `priority` 字段
  - 统一字段命名

- ✅ `cloudfunctions/cloud-functions-admin/db/announcements.js`
  - 更新 `createAnnouncement` 方法，统一数据结构
  - 使用 `isSticky` 替代 `isVisible`

#### 云函数API层
- ✅ `cloudfunctions/cloud-functions-admin/api/announcements-admin.js`
  - 更新 `createAnnouncement` API，支持 `isSticky`, `priority` 参数
  - 更新 `updateAnnouncement` API，支持新字段
  - 修改 `deleteAnnouncement` API，改为硬删除
  - 添加优先级验证（1-10范围）

#### 后台管理前端
- ✅ `admin-app/src/views/Announcements/index.vue`
  - 表单添加"是否置顶"开关
  - 表单添加"优先级"选择器（1-10）
  - 表格添加置顶和优先级列显示
  - 修改删除确认对话框，明确是永久删除
  - 更新表单数据结构和重置函数

- ✅ `admin-app/src/api/announcements.js`
  - 移除删除API的软删除参数
  - 更新API注释

#### 文档更新
- ✅ `cloudfunctions/README.md`
  - 更新公告数据结构说明
  - 统一字段定义和注释

## 🎯 功能改进

### 1. 新增字段功能

**是否置顶 (isSticky)**：
- 类型：Boolean
- 默认值：false
- 功能：置顶公告会优先显示
- 前端显示：表格中显示📌图标

**优先级 (priority)**：
- 类型：Number (1-10)
- 默认值：5
- 功能：数字越大优先级越高
- 前端显示：不同颜色的标签
  - 8-10：红色（高优先级）
  - 6-7：橙色（中优先级）
  - 1-5：蓝色（低优先级）

### 2. 删除操作改进

**硬删除**：
- 移除软删除逻辑
- 直接物理删除记录
- 更新确认对话框文案
- 明确提示"此操作不可恢复"

### 3. 表单验证

**新增验证规则**：
- 优先级必须在1-10之间
- 公告类型限制为：announcement, notice, update, maintenance
- 标题不超过100字符
- 内容不超过5000字符

## 🔄 数据迁移

由于这是开发中的新项目，不需要数据迁移。新的数据结构将直接应用于新创建的公告。

## ✅ 验证清单

- [x] 云函数数据库层统一字段定义
- [x] 云函数API层支持新字段
- [x] 删除操作改为硬删除
- [x] 后台管理表单添加新字段
- [x] 表格显示新字段
- [x] 删除确认对话框更新
- [x] API参数验证
- [x] 文档更新

## 🚀 部署说明

### 1. 云函数部署
```bash
# 部署用户端云函数
cd cloudfunctions/cloud-functions
npm run deploy

# 部署管理端云函数
cd cloudfunctions/cloud-functions-admin
npm run deploy
```

### 2. 后台管理应用
```bash
cd admin-app
pnpm install
pnpm tauri dev  # 开发模式
pnpm tauri build  # 构建应用
```

## 📝 使用说明

### 创建公告
1. 在后台管理系统中点击"创建公告"
2. 填写标题、内容（支持Markdown）
3. 选择公告类型
4. 设置是否置顶
5. 选择优先级（1-10）
6. 设置发布时间和过期时间（可选）
7. 选择状态（草稿/已发布）

### 管理公告
- **查看**：点击查看按钮预览公告内容
- **编辑**：点击编辑按钮修改公告信息
- **删除**：点击删除按钮永久删除公告（不可恢复）
- **批量操作**：选择多个公告进行批量删除

### 公告显示规则
1. 置顶公告优先显示
2. 按优先级排序（高优先级在前）
3. 同优先级按发布时间倒序
4. 过期公告自动隐藏

现在公告系统已经完善，支持置顶、优先级管理，并改为硬删除操作。

## 🔄 第二次修改 (2025-08-14)

根据用户需求，进行了以下调整：

### 📝 修改内容

1. **移除字段**：
   - 去掉 `priority`（优先级）字段
   - 去掉 `viewCount`（查看次数）字段

2. **用户权限调整**：
   - **测试用户**能够看到任何 `status` 的公告（包括草稿、已发布、已归档）
   - **测试用户**能够看到过期的公告（不再过滤 `expiryTime`）
   - **普通用户**仍然只能看到已发布且未过期的公告

### 🛠️ 修改的文件

#### 云函数数据库层
- ✅ `cloudfunctions/cloud-functions/db/announcements.js`
  - 移除 `priority` 和 `viewCount` 字段
- ✅ `cloudfunctions/cloud-functions-admin/db/announcements.js`
  - 移除 `priority` 和 `viewCount` 字段

#### 云函数API层
- ✅ `cloudfunctions/cloud-functions/db/announcements.js`
  - 修改 `getPublishedAnnouncements` 方法，根据用户类型决定过滤条件
  - 测试用户可以看到所有状态和过期的公告，普通用户仍有限制
- ✅ `cloudfunctions/cloud-functions/api/announcements.js`
  - 获取当前用户信息，判断是否为测试用户
  - 将测试用户标识传递给数据库查询方法
- ✅ `cloudfunctions/cloud-functions-admin/api/announcements-admin.js`
  - 移除创建和更新API中的 `priority` 参数处理
  - 移除优先级验证逻辑

#### 后台管理前端
- ✅ `admin-app/src/views/Announcements/index.vue`
  - 移除优先级表单项
  - 移除优先级表格列
  - 更新表单数据结构和重置函数

#### 文档更新
- ✅ `cloudfunctions/README.md`
  - 更新公告数据结构说明
- ✅ `ANNOUNCEMENT_SYSTEM_IMPROVEMENTS.md`
  - 添加第二次修改说明

### 🎯 最终数据结构

```javascript
{
  _id: ObjectId,              // 自动生成的ID
  title: String,              // 公告标题
  content: String,            // Markdown格式内容
  type: String,               // 类型：'announcement', 'update', 'notice', 'maintenance'
  isSticky: Boolean,          // 是否置顶
  status: String,             // 状态：'draft', 'published', 'archived'
  publishTime: Date,          // 发布时间
  expiryTime: Date,           // 过期时间（可选，null表示不过期）
  createdAt: Date,            // 创建时间
  updatedAt: Date             // 更新时间
}
```

### 📱 用户体验变化

1. **用户端（小程序）**：
   - **测试用户**：可以看到所有状态的公告（草稿、已发布、已归档）和过期公告
   - **普通用户**：只能看到已发布且未过期的公告
   - 置顶公告仍然优先显示

2. **管理端**：
   - 表单更简洁，移除了优先级选择
   - 表格显示更清晰，专注于核心信息
   - 硬删除操作保持不变

### ✅ 验证清单

- [x] 移除 priority 和 viewCount 字段
- [x] 测试用户可以看到任何状态的公告
- [x] 测试用户可以看到过期的公告
- [x] 普通用户仍然只能看到已发布且未过期的公告
- [x] 后台管理表单移除优先级字段
- [x] 表格移除优先级列
- [x] 文档更新完成

### 🚀 部署说明

修改完成后需要重新部署云函数：

```bash
# 部署用户端云函数
cd cloudfunctions/cloud-functions
npm run deploy

# 部署管理端云函数
cd cloudfunctions/cloud-functions-admin
npm run deploy
```

### 🔧 测试用户设置

要让用户成为测试用户，需要在数据库中手动设置：

1. **通过云开发控制台**：
   - 进入微信云开发控制台
   - 选择数据库 → users 集合
   - 找到目标用户记录
   - 将 `isTestUser` 字段设置为 `true`

2. **通过后台管理系统**：
   - 在用户管理页面编辑用户
   - 勾选"测试用户"选项
   - 保存修改

**注意**：测试用户标识只能通过管理员操作设置，用户注册时默认为普通用户。

现在公告系统更加简洁，测试用户可以看到所有公告内容进行测试，普通用户只看到正常发布的公告，管理界面也更加清晰易用。

## 🔄 第三次修改 (2025-08-14)

根据用户需求，移除了批量操作功能：

### 📝 修改内容

**移除批量操作功能**：
- 移除表格的选择框列
- 移除批量操作按钮和界面
- 移除批量删除、批量更新状态等功能
- 简化界面，专注于单个公告的操作

### 🛠️ 修改的文件

#### 后台管理前端
- ✅ `admin-app/src/views/Announcements/index.vue`
  - 移除表格的 `type="selection"` 列
  - 移除批量操作的UI组件和按钮
  - 移除批量操作相关的CSS样式
  - 移除 `selectedAnnouncements` 状态变量
  - 移除 `handleSelectionChange`、`batchUpdateStatus`、`batchDelete` 方法

#### API文件
- ✅ `admin-app/src/api/announcements.js`
  - 移除 `batchOperateAnnouncements` 方法
  - 清理多余的空行

### 🎯 界面简化效果

**移除的功能**：
- ❌ 表格选择框
- ❌ 批量选择状态显示
- ❌ 批量操作按钮（批量发布、批量下架、批量删除）
- ❌ 批量操作确认对话框

**保留的功能**：
- ✅ 单个公告的查看、编辑、删除
- ✅ 公告创建和更新
- ✅ 状态管理（草稿、已发布、已归档）
- ✅ 置顶功能
- ✅ 搜索和筛选

### 📱 用户体验改进

1. **界面更简洁**：
   - 移除了复杂的批量操作界面
   - 表格更清爽，专注于内容展示
   - 操作更直观，避免误操作

2. **操作更安全**：
   - 每个删除操作都需要单独确认
   - 避免批量误删的风险
   - 操作更加谨慎和可控

3. **维护更简单**：
   - 减少了代码复杂度
   - 降低了维护成本
   - 功能更加专注

### ✅ 验证清单

- [x] 移除表格选择框
- [x] 移除批量操作UI组件
- [x] 移除批量操作相关方法
- [x] 移除批量操作API
- [x] 清理相关CSS样式
- [x] 清理不使用的变量和导入

现在公告管理系统更加简洁高效，专注于单个公告的精细化管理，避免了批量操作可能带来的风险。
