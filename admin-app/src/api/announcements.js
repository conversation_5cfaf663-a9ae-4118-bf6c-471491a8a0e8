/**
 * 公告管理API
 */

import { callCloudFunction } from './wechat-api.js'

/**
 * 创建公告
 * @param {Object} data - 公告数据
 * @returns {Promise} 创建结果
 */
export function createAnnouncement(data) {
  return callCloudFunction('createAnnouncement', data, { showSuccess: true })
}

/**
 * 更新公告
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
export function updateAnnouncement(data) {
  return callCloudFunction('updateAnnouncement', data, { showSuccess: true })
}

/**
 * 删除公告（硬删除）
 * @param {string} id - 公告ID
 * @returns {Promise} 删除结果
 */
export function deleteAnnouncement(id) {
  return callCloudFunction('deleteAnnouncement', { id }, { showSuccess: true })
}

/**
 * 获取公告列表（管理端）
 * @param {Object} params - 查询参数
 * @returns {Promise} 公告列表
 */
export function getAnnouncementList(params = {}) {
  return callCloudFunction('getAnnouncementListAdmin', params)
}

/**
 * 获取公告统计
 * @param {string} period - 统计周期
 * @returns {Promise} 统计数据
 */
export function getAnnouncementStats(period = '30d') {
  return callCloudFunction('getAnnouncementStats', { period })
}