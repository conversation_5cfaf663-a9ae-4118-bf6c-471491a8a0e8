<template>
  <div class="announcements-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <MegaphoneIcon :size="24" />
          公告管理
        </h1>
        <p class="page-description">管理系统公告和通知</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="success" @click="showCreateDialog">
          <template #icon>
            <PlusIcon :size="16" />
          </template>
          创建公告
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <MegaphoneIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总公告数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon published">
          <CheckCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.published || 0 }}</div>
          <div class="stat-label">已发布</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon draft">
          <EditIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.draft || 0 }}</div>
          <div class="stat-label">草稿</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon archived">
          <ArchiveIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.archived || 0 }}</div>
          <div class="stat-label">已归档</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="类型">
          <el-select v-model="filters.type" placeholder="选择类型" style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="公告" value="announcement" />
            <el-option label="通知" value="notice" />
            <el-option label="更新" value="update" />
            <el-option label="维护" value="maintenance" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索标题或内容"
            style="width: 250px"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <SearchIcon :size="16" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <template #icon>
              <SearchIcon :size="16" />
            </template>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <template #icon>
              <RotateCcwIcon :size="16" />
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 公告列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="announcementList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isSticky" label="置顶" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.isSticky" type="success" size="small">✅</el-tag>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.publishTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewAnnouncement(row)"
            >
              <template #icon>
                <EyeIcon :size="14" />
              </template>
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="editAnnouncement(row)"
            >
              <template #icon>
                <EditIcon :size="14" />
              </template>
              编辑
            </el-button>
            <el-button
              v-if="row.status !== 'archived'"
              type="info"
              size="small"
              @click="archiveAnnouncement(row)"
            >
              <template #icon>
                <ArchiveIcon :size="14" />
              </template>
              归档
            </el-button>
            <el-button
              v-if="row.status === 'archived'"
              type="success"
              size="small"
              @click="unarchiveAnnouncement(row)"
            >
              <template #icon>
                <RotateCcwIcon :size="14" />
              </template>
              取消归档
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteAnnouncement(row)"
            >
              <template #icon>
                <TrashIcon :size="14" />
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 公告编辑对话框 -->
    <el-dialog
      v-model="announcementDialogVisible"
      :title="isEditing ? '编辑公告' : '创建公告'"
      width="800px"
      @close="resetAnnouncementForm"
    >
      <el-form
        ref="announcementFormRef"
        :model="announcementForm"
        :rules="announcementRules"
        label-width="100px"
      >
        <el-form-item label="类型" prop="type">
          <el-select v-model="announcementForm.type" style="width: 200px">
            <el-option label="公告" value="announcement" />
            <el-option label="通知" value="notice" />
            <el-option label="更新" value="update" />
            <el-option label="维护" value="maintenance" />
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="announcementForm.title" placeholder="请输入公告标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <MarkdownEditor
            v-model="announcementForm.content"
            placeholder="请输入公告内容（支持Markdown格式）"
            :rows="12"
          />
        </el-form-item>
        <el-form-item label="是否置顶" prop="isSticky">
          <el-switch v-model="announcementForm.isSticky" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="announcementForm.status" style="width: 200px">
            <el-option label="草稿" value="draft" />
            <el-option label="发布" value="published" />
          </el-select>
        </el-form-item>
        <el-form-item label="发布时间" v-if="announcementForm.status === 'published'">
          <el-date-picker
            v-model="announcementForm.publishTime"
            type="datetime"
            placeholder="选择发布时间"
            style="width: 200px"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="announcementDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="saving"
          @click="saveAnnouncement"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 公告查看对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="查看公告"
      width="700px"
    >
      <div v-if="currentAnnouncement" class="announcement-detail">
        <div class="detail-header">
          <h2>{{ currentAnnouncement.title }}</h2>
          <div class="detail-meta">
            <el-tag :type="getTypeColor(currentAnnouncement.type)" size="small">
              {{ getTypeText(currentAnnouncement.type) }}
            </el-tag>
            <el-tag :type="getStatusColor(currentAnnouncement.status)" size="small">
              {{ getStatusText(currentAnnouncement.status) }}
            </el-tag>
          </div>
        </div>
        <div class="detail-content">
          <div class="markdown-preview" v-html="renderedContent"></div>
        </div>
        <div class="detail-footer">
          <p class="time-info">
            创建时间: {{ formatDate(currentAnnouncement.createTime) }}
          </p>
          <p v-if="currentAnnouncement.publishTime" class="time-info">
            发布时间: {{ formatDate(currentAnnouncement.publishTime) }}
          </p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Megaphone as MegaphoneIcon,
  CheckCircle as CheckCircleIcon,
  Edit as EditIcon,
  Eye as EyeIcon,
  RefreshCw as RefreshCwIcon,
  Plus as PlusIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  Trash as TrashIcon,
  Archive as ArchiveIcon
} from 'lucide-vue-next'
import { callCloudFunction } from '@/api/wechat-api.js'
import MarkdownEditor from '@/components/MarkdownEditor.vue'
import { marked } from 'marked'

// 响应式数据
const route = useRoute()
const loading = ref(false)
const saving = ref(false)
const announcementList = ref([])
const stats = ref({})
const announcementDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEditing = ref(false)
const currentAnnouncement = ref(null)
const announcementFormRef = ref()

const filters = reactive({
  type: 'all',
  status: 'all',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const announcementForm = reactive({
  title: '',
  content: '',
  type: 'announcement',
  isSticky: false,
  status: 'draft',
  publishTime: null
})

const announcementRules = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择公告类型', trigger: 'change' }
  ]
}

// 计算属性
const renderedContent = computed(() => {
  if (!currentAnnouncement.value?.content) return ''
  try {
    return marked(currentAnnouncement.value.content)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return currentAnnouncement.value.content
  }
})

// 方法
async function loadAnnouncementList() {
  try {
    loading.value = true
    
    const result = await callCloudFunction('getAnnouncementListAdmin', {
      ...filters,
      page: pagination.page,
      pageSize: pagination.pageSize
    })

    if (result.success) {
      announcementList.value = result.data || []
      pagination.total = result.pagination?.total || 0
    } else {
      ElMessage.error(result.message || '获取公告列表失败')
    }
  } catch (error) {
    console.error('获取公告列表失败:', error)
    ElMessage.error('获取公告列表失败')
  } finally {
    loading.value = false
  }
}

async function loadStats() {
  try {
    const result = await callCloudFunction('getAnnouncementStats', { period: '30d' })
    if (result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取公告统计失败:', error)
  }
}

function handleSearch() {
  pagination.page = 1
  loadAnnouncementList()
}

function resetFilters() {
  filters.type = 'all'
  filters.status = 'all'
  filters.keyword = ''
  pagination.page = 1
  loadAnnouncementList()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadAnnouncementList()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadAnnouncementList()
}

async function refreshData() {
  await Promise.all([loadAnnouncementList(), loadStats()])
  ElMessage.success('数据已刷新')
}

function showCreateDialog() {
  isEditing.value = false
  resetAnnouncementForm()
  announcementDialogVisible.value = true
}

function viewAnnouncement(announcement) {
  currentAnnouncement.value = announcement
  viewDialogVisible.value = true
}

function editAnnouncement(announcement) {
  isEditing.value = true
  Object.assign(announcementForm, {
    ...announcement,
    publishTime: announcement.publishTime ? new Date(announcement.publishTime) : null
  })
  announcementDialogVisible.value = true
}

function resetAnnouncementForm() {
  Object.assign(announcementForm, {
    title: '',
    content: '',
    type: 'announcement',
    isSticky: false,
    status: 'draft',
    publishTime: null
  })
  announcementFormRef.value?.clearValidate()
}

async function saveAnnouncement() {
  try {
    const valid = await announcementFormRef.value.validate()
    if (!valid) return

    saving.value = true

    const apiType = isEditing.value ? 'updateAnnouncement' : 'createAnnouncement'
    const data = {
      ...announcementForm,
      publishTime: announcementForm.publishTime ? announcementForm.publishTime.toISOString() : null
    }

    if (isEditing.value) {
      data.id = currentAnnouncement.value?._id
    }

    const result = await callCloudFunction(apiType, data)

    if (result.success) {
      ElMessage.success(isEditing.value ? '公告更新成功' : '公告创建成功')
      announcementDialogVisible.value = false
      loadAnnouncementList()
    } else {
      ElMessage.error(result.message || '保存公告失败')
    }
  } catch (error) {
    console.error('保存公告失败:', error)
    ElMessage.error('保存公告失败')
  } finally {
    saving.value = false
  }
}

async function deleteAnnouncement(announcement) {
  try {
    await ElMessageBox.confirm(
      `确定要永久删除公告 "${announcement.title}" 吗？此操作不可恢复！`,
      '永久删除公告',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    const result = await callCloudFunction('deleteAnnouncement', {
      id: announcement._id
    })

    if (result.success) {
      ElMessage.success('公告删除成功')
      loadAnnouncementList()
    } else {
      ElMessage.error(result.message || '删除公告失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除公告失败:', error)
      ElMessage.error('删除公告失败')
    }
  }
}

async function archiveAnnouncement(announcement) {
  try {
    await ElMessageBox.confirm(
      `确定要归档公告 "${announcement.title}" 吗？`,
      '归档公告',
      {
        confirmButtonText: '确定归档',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await callCloudFunction('updateAnnouncement', {
      _id: announcement._id,
      status: 'archived'
    })

    if (result.success) {
      ElMessage.success('公告归档成功')
      loadAnnouncementList()
    } else {
      ElMessage.error(result.message || '归档公告失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('归档公告失败:', error)
      ElMessage.error('归档公告失败')
    }
  }
}

async function unarchiveAnnouncement(announcement) {
  try {
    await ElMessageBox.confirm(
      `确定要取消归档公告 "${announcement.title}" 吗？`,
      '取消归档',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const result = await callCloudFunction('updateAnnouncement', {
      _id: announcement._id,
      status: 'published'
    })

    if (result.success) {
      ElMessage.success('取消归档成功')
      loadAnnouncementList()
    } else {
      ElMessage.error(result.message || '取消归档失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消归档失败:', error)
      ElMessage.error('取消归档失败')
    }
  }
}

// 辅助函数
function getTypeColor(type) {
  const colors = {
    announcement: 'primary',
    notice: 'success',
    update: 'warning',
    maintenance: 'danger'
  }
  return colors[type] || 'info'
}

function getTypeText(type) {
  const texts = {
    announcement: '公告',
    notice: '通知',
    update: '更新',
    maintenance: '维护'
  }
  return texts[type] || type
}

function getStatusColor(status) {
  const colors = {
    published: 'success',
    draft: 'warning',
    archived: 'info'
  }
  return colors[status] || 'info'
}

function getStatusText(status) {
  const texts = {
    published: '已发布',
    draft: '草稿',
    archived: '已归档'
  }
  return texts[status] || status
}

function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadAnnouncementList()
  loadStats()

  // 检查URL参数，如果有action=create，自动打开创建对话框
  if (route.query.action === 'create') {
    createAnnouncement()
  }
})
</script>

<style scoped>
.announcements-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.published { background: #10b981; }
.stat-icon.draft { background: #f59e0b; }
.stat-icon.archived { background: #6b7280; }
.stat-icon.views { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.announcement-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-header h2 {
  margin: 0 0 10px 0;
  color: #1f2937;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 14px;
  color: #6b7280;
}

.detail-content {
  margin: 20px 0;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  line-height: 1.6;
}

.detail-footer {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.time-info {
  margin: 4px 0;
  font-size: 14px;
  color: #6b7280;
}
</style>
