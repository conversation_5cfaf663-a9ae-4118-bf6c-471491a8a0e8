# 微信小程序云函数项目

本项目包含微信小程序的云函数服务，提供用户端和管理端的完整API支持。

## 📁 项目结构

```
cloudfunctions/
├── cloud-functions/        # 用户端云函数
│   ├── api/                # API模块
│   ├── db/                 # 数据库操作
│   ├── utils/              # 工具函数
│   └── index.js            # 入口文件
├── cloud-functions-admin/  # 管理端云函数
│   ├── db/                 # 数据库操作
│   ├── utils/              # 工具函数
│   └── index.js            # 入口文件
└── README.md               # 项目文档
```

## 🗄️ 数据库集合设计

### 核心用户数据

#### 1. `users` - 用户基础信息
```javascript
{
  _id: "用户唯一ID",
  openid: "微信OpenID",
  no: "用户编号",
  nickname: "用户昵称",
  avatar: "头像URL",
  status: "active",         // 用户状态: active(正常), inactive(屏蔽), banned(封禁)
  isTestUser: false,        // 是否测试用户
  points: 0,                // 积分余额（冗余字段，实时从积分记录计算）
  apiCallCount: 0,          // API调用次数
  vip: {                    // VIP信息（冗余字段，实时从VIP记录计算）
    status: false,          // VIP状态
    expiredAt: "2025-12-31T23:59:59.999Z"  // 过期时间
  },
  checkInStats: {           // 签到统计
    totalDays: 0,           // 总签到天数
    consecutiveDays: 0,     // 连续签到天数
    lastCheckIn: "2025-08-13T00:00:00.000Z"  // 最后签到时间
  },
  invitation: {             // 邀请信息
    code: "INVITE123",      // 邀请码
    invitedCount: 0         // 邀请人数
  },
  settings: {},             // 用户设置
  version: "1.0.0",         // 数据版本
  createTime: "2025-08-13T00:00:00.000Z",
  updateTime: "2025-08-13T00:00:00.000Z"
}
```

#### 2. `user-data` - 用户数据存储
```javascript
{
  _id: "记录ID",
  userId: "用户ID",
  data: {},                 // 用户数据内容
  recordDate: "2025-08-13", // 记录日期 (YYYY-MM-DD)
  lastModified: "2025-08-13T00:00:00.000Z",
  createTime: "2025-08-13T00:00:00.000Z"
}
```

### 内容管理

#### 3. `announcements` - 公告管理
```javascript
{
  _id: "公告ID",
  title: "公告标题",
  content: "公告内容（Markdown格式）",
  type: "announcement",     // 类型: announcement(公告), notice(通知), update(更新), maintenance(维护)
  isSticky: false,          // 是否置顶
  status: "published",      // 状态: draft(草稿), published(已发布), archived(已归档)
  publishTime: "2025-08-13T00:00:00.000Z",  // 发布时间
  expiryTime: null,         // 过期时间（null表示不过期）
  createdAt: "2025-08-13T00:00:00.000Z",   // 创建时间
  updatedAt: "2025-08-13T00:00:00.000Z"    // 更新时间
}
```

#### 4. `feedback` - 用户反馈
```javascript
{
  _id: "反馈ID",
  userId: "用户ID",
  userNumber: "用户编号",
  type: "bug",              // 类型: bug, feature, suggestion, other
  title: "反馈标题",
  content: "反馈内容",
  email: "联系邮箱",         // 可选
  status: "pending",       // 状态: pending, processing, resolved, closed
  priority: 2,             // 优先级
  adminReply: "管理员回复",  // 可选
  replyTime: "2025-08-13T00:00:00.000Z",  // 回复时间
  createTime: "2025-08-13T00:00:00.000Z",
  updateTime: "2025-08-13T00:00:00.000Z"
}
```

### 积分系统

#### 5. `points-records` - 积分记录
```javascript
{
  _id: "记录ID",
  userId: "用户ID",
  userNumber: "用户编号",
  type: "earn",             // 类型: earn(获得), spend(消费)
  amount: 10,               // 积分数量 (正数为获得，负数为消费)
  source: "check_in",       // 来源: check_in, purchase, admin, redeem
  description: "每日签到奖励",
  relatedId: "关联ID",      // 可选，关联的订单或活动ID
  timestamp: "2025-08-13T00:00:00.000Z"
}
```

#### 6. `store-items` - 商店商品
```javascript
{
  _id: "商品ID",
  name: "商品名称",
  description: "商品描述",
  price: 100,               // 积分价格
  category: "digital",      // 分类: digital, physical, service
  imageUrl: "商品图片URL",
  isAvailable: true,        // 是否可用
  stock: 100,               // 库存数量 (-1表示无限)
  sortOrder: 0,             // 排序权重
  tags: ["热门", "限时"],     // 标签
  createTime: "2025-08-13T00:00:00.000Z",
  updateTime: "2025-08-13T00:00:00.000Z"
}
```

#### 7. `redemption-codes` - 兑换码
```javascript
{
  _id: "兑换码ID",
  code: "REDEEM123",        // 兑换码
  type: "points",           // 类型: points, item, discount
  value: 100,               // 值 (积分数量、商品ID或折扣百分比)
  description: "新用户奖励",
  isActive: true,           // 是否激活
  usageLimit: 1,            // 使用次数限制
  usedCount: 0,             // 已使用次数
  expiresAt: "2025-12-31T23:59:59.999Z",  // 过期时间
  createdBy: "admin",       // 创建者
  lastUsedAt: "2025-08-13T00:00:00.000Z", // 最后使用时间
  lastUsedBy: "用户ID",     // 最后使用者
  createTime: "2025-08-13T00:00:00.000Z"
}
```

### 功能模块

#### 8. `check-ins` - 签到记录
```javascript
{
  _id: "签到ID",
  userId: "用户ID",
  date: "2025-08-13",       // 签到日期 (YYYY-MM-DD)
  checkInAt: "2025-08-13T08:30:00.000Z",  // 签到时间
  consecutiveDays: 5,       // 连续签到天数
  reward: 10,               // 获得积分
  bonusReward: 5,           // 连续奖励
  location: {               // 签到位置 (可选)
    latitude: 39.9042,
    longitude: 116.4074,
    address: "北京市朝阳区"
  }
}
```

#### 9. `fishing-status` - 摸鱼状态
```javascript
{
  _id: "摸鱼ID",
  userId: "用户ID",
  openid: "微信OpenID",
  workId: "工作ID",
  startTime: "2025-08-13T09:00:00.000Z",  // 开始时间
  endTime: "2025-08-13T17:00:00.000Z",    // 结束时间 (可选)
  startMinutes: 540,        // 开始时间的分钟数 (9:00 = 540)
  workSegment: {            // 工作时间段
    start: 540,             // 开始分钟数
    end: 1020,              // 结束分钟数
    name: "上午"
  },
  maxEndTime: "2025-08-13T17:00:00.000Z", // 最大结束时间
  remark: "摸鱼备注",
  status: "active",         // 状态: active, completed, expired
  endedBy: "user",          // 结束方式: user, admin, system
  location: {               // 摸鱼位置 (可选)
    latitude: 39.9042,
    longitude: 116.4074,
    address: "办公室"
  }
}
```

#### 10. `friend-apps` - 友情应用
```javascript
{
  _id: "应用ID",
  name: "应用名称",
  description: "应用描述",
  url: "跳转链接",
  icon: "图标URL",
  category: "tool",         // 分类: tool, game, education, life, other
  sortOrder: 0,             // 排序权重
  visible: true,            // 是否可见
  clickCount: 0,            // 点击次数
  createTime: "2025-08-13T00:00:00.000Z",
  updateTime: "2025-08-13T00:00:00.000Z"
}
```

### 系统管理

#### 11. `vip-records` - VIP记录
```javascript
{
  _id: "记录ID",
  userId: "用户ID",
  userNumber: "用户编号",
  action: "activate",       // 操作: activate, extend, expire, cancel
  duration: 30,             // 时长 (天数)
  startDate: "2025-08-13T00:00:00.000Z",  // 开始时间
  endDate: "2025-09-12T23:59:59.999Z",    // 结束时间
  source: "purchase",       // 来源: purchase, gift, manual
  description: "购买VIP会员",
  price: 99.00,             // 价格 (可选)
  relatedId: "订单ID",      // 关联ID (可选)
  createTime: "2025-08-13T00:00:00.000Z"
}
```

#### 12. `cache-data` - 缓存数据
```javascript
{
  _id: "缓存ID",
  key: "缓存键",
  value: {},                // 缓存值
  expireTime: "2025-08-13T01:00:00.000Z",  // 过期时间
  createTime: "2025-08-13T00:00:00.000Z"
}
```

#### 13. `system_config` - 系统配置
```javascript
{
  _id: "配置ID",           // 如: checkin_config, cache_config
  // 签到配置示例
  basePoints: 10,           // 基础积分
  consecutiveBonus: 5,      // 连续奖励
  consecutiveThreshold: 3,  // 连续阈值
  dailyLimit: true,         // 每日限制
  timeRange: [],            // 时间范围
  updateTime: "2025-08-13T00:00:00.000Z"
}
```

## 🔧 开发流程

### 1. 环境准备
1. 安装微信开发者工具
2. 配置云开发环境
3. 设置环境变量和密钥

### 2. 开发规范
1. **API命名**: 使用驼峰命名法，动词+名词结构
2. **错误处理**: 统一使用success/error响应格式
3. **数据验证**: 所有输入参数必须验证
4. **日志记录**: 关键操作必须记录日志

### 3. 数据库规范
1. **集合命名**: 使用小写字母和连字符
2. **字段命名**: 使用驼峰命名法
3. **时间字段**: 统一使用ISO 8601格式
4. **索引优化**: 为常用查询字段建立索引

### 4. 部署流程
1. 本地测试通过
2. 上传云函数代码
3. 配置触发器和权限
4. 生产环境验证

## 🛡️ 安全规范

### 1. 权限控制
- 用户端API: 验证用户身份
- 管理端API: 验证SECRET_KEY
- 敏感操作: 双重验证

### 2. 数据保护
- 用户隐私数据加密存储
- 敏感信息不记录日志
- 定期清理过期数据

### 3. 接口安全
- 参数验证和过滤
- SQL注入防护
- 频率限制

## 📊 监控和维护

### 1. 性能监控
- API响应时间
- 数据库查询性能
- 缓存命中率

### 2. 错误监控
- 异常日志收集
- 错误率统计
- 告警机制

### 3. 数据维护
- 定期备份
- 数据清理
- 索引优化

## 🚀 扩展计划

### 1. 功能扩展
- 消息推送系统
- 数据分析平台
- 第三方集成

### 2. 性能优化
- 缓存策略优化
- 数据库分片
- CDN加速

### 3. 运维自动化
- 自动部署
- 监控告警
- 故障恢复
