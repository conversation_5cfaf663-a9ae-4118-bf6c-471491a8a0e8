/**
 * 公告管理API（管理端）
 * 提供完整的公告CRUD管理功能
 */

const { success, error, paginated, statsData, invalidParam, notFound } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired } = require('../utils/validators')

// 使用本地的数据库操作类
const announcementsDB = require('../db/announcements')

/**
 * 创建公告（管理端）
 */
exports.createAnnouncement = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['title', 'content', 'type'])
  if (!validation.success) {
    return validation
  }

  const { title, content, type, status, publishTime, expiryTime, isVisible } = params

  // 验证公告类型
  const validTypes = ['announcement', 'notice', 'update', 'maintenance', 'promotion']
  if (!validTypes.includes(type)) {
    return invalidParam('type', `公告类型必须是: ${validTypes.join(', ')}`)
  }

  // 验证状态
  const validStatuses = ['draft', 'published', 'archived']
  if (status && !validStatuses.includes(status)) {
    return invalidParam('status', `状态必须是: ${validStatuses.join(', ')}`)
  }

  // 验证内容长度
  if (title.length > 100) {
    return invalidParam('title', '标题不能超过100字符')
  }

  if (content.length > 5000) {
    return invalidParam('content', '内容不能超过5000字符')
  }

  try {
    const announcementData = {
      title: title.trim(),
      content: content.trim(),
      type,
      publishTime: publishTime ? new Date(publishTime) : new Date(),
      expiryTime: expiryTime ? new Date(expiryTime) : null,
      isVisible: isVisible !== false, // 默认可见
      status: status || 'draft' // 默认为草稿
    }

    const result = await announcementsDB.createAnnouncement(announcementData)
    
    if (!result.success) {
      return error(result.message || '创建公告失败')
    }

    // 清理相关缓存
    console.log('[AnnouncementsAdmin] 公告创建成功，清理统计缓存')

    return success(result.data, '公告创建成功')
  } catch (err) {
    console.error('创建公告失败:', err)
    return error('创建公告失败')
  }
})

/**
 * 更新公告（管理端）
 */
exports.updateAnnouncement = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['_id'])
  if (!validation.success) {
    return validation
  }

  const { _id, title, content, type, publishTime, expiryTime, isVisible, status } = params

  try {
    // 检查公告是否存在
    const existingResult = await announcementsDB.getAnnouncementById(_id)
    if (!existingResult.success || !existingResult.data) {
      return notFound('公告')
    }

    // 构建更新数据
    const updateData = {}
    if (title !== undefined) {
      if (title.length > 100) {
        return invalidParam('title', '标题不能超过100字符')
      }
      updateData.title = title.trim()
    }
    
    if (content !== undefined) {
      if (content.length > 5000) {
        return invalidParam('content', '内容不能超过5000字符')
      }
      updateData.content = content.trim()
    }
    
    if (type !== undefined) {
      const validTypes = ['announcement', 'notice', 'update', 'maintenance', 'promotion']
      if (!validTypes.includes(type)) {
        return invalidParam('type', `公告类型必须是: ${validTypes.join(', ')}`)
      }
      updateData.type = type
    }

    if (publishTime !== undefined) {
      updateData.publishTime = new Date(publishTime)
    }
    
    if (expiryTime !== undefined) {
      updateData.expiryTime = expiryTime ? new Date(expiryTime) : null
    }
    
    if (isVisible !== undefined) {
      updateData.isVisible = isVisible
    }
    
    if (status !== undefined) {
      const validStatuses = ['draft', 'published', 'archived']
      if (!validStatuses.includes(status)) {
        return invalidParam('status', `状态必须是: ${validStatuses.join(', ')}`)
      }
      updateData.status = status
    }

    const result = await announcementsDB.updateAnnouncement(_id, updateData)
    
    if (!result.success) {
      return error(result.message || '更新公告失败')
    }

    return success(result.data, '公告更新成功')
  } catch (err) {
    console.error('更新公告失败:', err)
    return error('更新公告失败')
  }
})

/**
 * 删除公告（管理端）
 */
exports.deleteAnnouncement = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['id'])
  if (!validation.success) {
    return validation
  }

  const { id, softDelete = true } = params

  try {
    // 检查公告是否存在
    const existingResult = await announcementsDB.getAnnouncementById(id)
    if (!existingResult.success || !existingResult.data) {
      return notFound('公告')
    }

    let result
    if (softDelete) {
      // 软删除：更新状态为已删除
      result = await announcementsDB.updateAnnouncement(id, { 
        status: 'deleted',
        deletedAt: new Date()
      })
    } else {
      // 硬删除：物理删除记录
      result = await announcementsDB.deleteAnnouncement(id)
    }
    
    if (!result.success) {
      return error(result.message || '删除公告失败')
    }

    // 清理相关缓存
    console.log('[AnnouncementsAdmin] 公告删除成功，清理统计缓存')

    return success(null, softDelete ? '公告已移至回收站' : '公告已永久删除')
  } catch (err) {
    console.error('删除公告失败:', err)
    return error('删除公告失败')
  }
})

/**
 * 获取公告列表（管理端）
 */
exports.getAnnouncementListAdmin = wrapAsync(async (params = {}) => {
  const {
    type = 'all',
    status = 'all',
    page = 1,
    pageSize = 20,
    sortBy = 'createTime',
    sortOrder = 'desc',
    keyword = ''
  } = params

  // 参数验证
  if (pageSize > 100) {
    return invalidParam('pageSize', '每页数量不能超过100条')
  }

  const skip = (page - 1) * pageSize
  const limit = pageSize

  try {
    const result = await announcementsDB.getAnnouncementsAdmin({
      type,
      status,
      keyword,
      sortBy,
      sortOrder,
      limit,
      skip
    })

    if (!result.success) {
      return error(result.message || '获取公告列表失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取公告列表成功'
    )
  } catch (err) {
    console.error('获取公告列表失败:', err)
    return error('获取公告列表失败')
  }
})

/**
 * 获取公告统计（管理端）
 */
exports.getAnnouncementStats = wrapAsync(async (params = {}) => {
  const { period = '30d' } = params

  try {
    const result = await announcementsDB.getAnnouncementStats(period)
    
    if (!result.success) {
      return error(result.message || '获取公告统计失败')
    }

    return statsData(result.data, '获取公告统计成功')
  } catch (err) {
    console.error('获取公告统计失败:', err)
    return error('获取公告统计失败')
  }
})
