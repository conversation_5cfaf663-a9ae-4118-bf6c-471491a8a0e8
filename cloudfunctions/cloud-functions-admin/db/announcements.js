/**
 * 公告数据库操作类（管理端）
 */

const BaseDB = require('./base')

class AnnouncementsDB extends BaseDB {
  constructor() {
    super('announcements')
  }

  /**
   * 创建公告
   * @param {Object} announcementData - 公告数据
   * @returns {Promise<Object>} 创建结果
   */
  async createAnnouncement(announcementData) {
    try {
      console.log('[AnnouncementsDB] 创建公告:', announcementData)

      const data = {
        title: announcementData.title,
        content: announcementData.content,
        type: announcementData.type || 'announcement',
        isSticky: announcementData.isSticky || false,
        status: announcementData.status || 'published',
        publishTime: announcementData.publishTime || new Date(),
        expiryTime: announcementData.expiryTime || null,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...announcementData
      }

      const result = await this.create(data)

      if (result.success) {
        console.log('[AnnouncementsDB] 公告创建成功:', result.data._id)
      }

      return result
    } catch (error) {
      console.error('[AnnouncementsDB] 创建公告失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 根据ID获取公告
   * @param {string} id - 公告ID
   * @returns {Promise<Object>} 查询结果
   */
  async getAnnouncementById(id) {
    try {
      return await this.findById(id)
    } catch (error) {
      console.error('[AnnouncementsDB] 获取公告失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 更新公告
   * @param {string} id - 公告ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateAnnouncement(id, updateData) {
    try {
      console.log(`[AnnouncementsDB] 更新公告: ${id}`)
      
      const result = await this.update({ _id: id }, updateData)
      
      if (result.success) {
        console.log(`[AnnouncementsDB] 公告更新成功: ${id}`)
      }
      
      return result
    } catch (error) {
      console.error(`[AnnouncementsDB] 更新公告失败: ${id}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 删除公告
   * @param {string} id - 公告ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteAnnouncement(id) {
    try {
      console.log(`[AnnouncementsDB] 删除公告: ${id}`)
      
      const result = await this.delete({ _id: id })
      
      if (result.success) {
        console.log(`[AnnouncementsDB] 公告删除成功: ${id}`)
      }
      
      return result
    } catch (error) {
      console.error(`[AnnouncementsDB] 删除公告失败: ${id}`, error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取公告列表（管理端）
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getAnnouncementsAdmin(options = {}) {
    try {
      const {
        type = 'all',
        status = 'all',
        keyword = '',
        sortBy = 'createTime',
        sortOrder = 'desc',
        limit = 20,
        skip = 0
      } = options

      console.log(`[AnnouncementsDB] 管理端获取公告列表: type=${type}, status=${status}, keyword=${keyword}`)

      // 构建查询条件
      const where = {}
      
      if (type !== 'all') {
        where.type = type
      }
      
      if (status !== 'all') {
        where.status = status
      }
      
      if (keyword) {
        where.$or = [
          { title: new RegExp(keyword, 'i') },
          { content: new RegExp(keyword, 'i') }
        ]
      }

      // 查询总数
      const countResult = await this.count(where)
      const total = countResult.success ? countResult.data : 0

      // 查询数据
      const dataResult = await this.find(where, {
        limit,
        skip,
        orderBy: sortBy,
        order: sortOrder
      })

      if (!dataResult.success) {
        return dataResult
      }

      console.log(`[AnnouncementsDB] 管理端公告查询成功，共 ${dataResult.data.length} 条记录，总计 ${total} 条`)

      return {
        success: true,
        data: {
          list: dataResult.data,
          total,
          hasMore: skip + dataResult.data.length < total
        }
      }
    } catch (error) {
      console.error('[AnnouncementsDB] 管理端获取公告列表失败:', error)
      return { 
        success: false, 
        message: error.message 
      }
    }
  }

  /**
   * 获取公告统计信息（管理端）
   * @param {string} period - 统计周期
   * @returns {Promise<Object>} 统计结果
   */
  async getAnnouncementStats(period = '30d') {
    try {
      console.log(`[AnnouncementsDB] 获取公告统计信息: ${period}`)

      // 计算时间范围
      let startDate = null
      const now = new Date()
      
      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        case 'all':
        default:
          startDate = null
          break
      }

      const baseWhere = {}
      if (startDate) {
        baseWhere.createTime = { $gte: startDate }
      }

      // 总公告数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 已发布的公告数
      const publishedResult = await this.count({ ...baseWhere, status: 'published' })
      const published = publishedResult.success ? publishedResult.data : 0

      const stats = {
        total,
        published,
        draft: total - published,
        period,
        generatedAt: new Date().toISOString()
      }

      console.log('[AnnouncementsDB] 公告统计信息获取成功')

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[AnnouncementsDB] 获取公告统计失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 按日期范围获取公告统计
   * @param {Date} startDate - 开始日期
   * @param {Date} endDate - 结束日期
   * @returns {Promise<Object>} 统计结果
   */
  async getAnnouncementStatsByDateRange(startDate, endDate) {
    try {
      console.log(`[AnnouncementsDB] 按日期范围获取公告统计: ${startDate.toISOString()} - ${endDate.toISOString()}`)

      const baseWhere = {
        createTime: {
          $gte: startDate.toISOString(),
          $lt: endDate.toISOString()
        }
      }

      // 总公告数
      const totalResult = await this.count(baseWhere)
      const total = totalResult.success ? totalResult.data : 0

      // 已发布的公告数
      const publishedResult = await this.count({ ...baseWhere, status: 'published' })
      const published = publishedResult.success ? publishedResult.data : 0

      const stats = {
        total,
        published,
        draft: total - published
      }

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('[AnnouncementsDB] 按日期范围获取公告统计失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }
}

module.exports = new AnnouncementsDB()
