/**
 * 公告数据库操作
 */

const BaseDB = require('./base')

class AnnouncementsDB extends BaseDB {
  constructor() {
    super('announcements')
  }

  /**
   * 创建公告
   * @param {Object} announcementData - 公告数据
   * @returns {Promise<Object>} 创建结果
   */
  async createAnnouncement(announcementData) {
    const defaultData = {
      title: announcementData.title,
      content: announcementData.content,
      type: announcementData.type || 'announcement', // announcement, update, notice, maintenance
      isSticky: announcementData.isSticky || false,
      priority: announcementData.priority || 5, // 1-10, 数字越大优先级越高
      status: announcementData.status || 'published', // draft, published, archived
      publishTime: announcementData.publishTime || new Date(),
      expiryTime: announcementData.expiryTime || null,
      viewCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...announcementData
    }

    return await this.create(defaultData)
  }

  /**
   * 获取已发布的公告列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getPublishedAnnouncements(options = {}) {
    const {
      type,
      limit = 20,
      skip = 0
    } = options

    // 构建查询条件
    const where = {
      status: 'published'
    }

    // 添加类型筛选
    if (type && type !== 'all') {
      where.type = type
    }

    // 添加过期时间筛选（排除已过期的公告）
    const now = new Date()
    where.$or = [
      { expiryTime: null },
      { expiryTime: { $gte: now } }
    ]

    try {
      // 查询公告列表，置顶公告优先，然后按发布时间倒序
      const result = await this.find(where, {
        orderBy: { field: 'publishTime', order: 'desc' },
        limit,
        skip
      })

      if (!result.success) {
        return result
      }

      // 对结果进行排序：置顶公告在前，非置顶公告在后
      const sortedList = result.data.sort((a, b) => {
        // 首先按置顶状态排序
        if (a.isSticky && !b.isSticky) return -1
        if (!a.isSticky && b.isSticky) return 1
        
        // 如果置顶状态相同，按发布时间倒序
        return new Date(b.publishTime) - new Date(a.publishTime)
      })

      // 检查是否还有更多数据
      const totalResult = await this.count(where)
      const hasMore = totalResult.success ? (skip + limit < totalResult.data.total) : false

      return {
        success: true,
        data: {
          list: sortedList,
          hasMore,
          total: totalResult.success ? totalResult.data.total : 0
        }
      }
    } catch (error) {
      console.error('[AnnouncementsDB] 获取公告列表失败:', error)
      return {
        success: false,
        message: '获取公告列表失败',
        error: error.message
      }
    }
  }

  /**
   * 根据ID获取公告
   * @param {string} id - 公告ID
   * @returns {Promise<Object>} 查询结果
   */
  async getAnnouncementById(id) {
    return await this.findById(id)
  }

  /**
   * 更新公告
   * @param {string} id - 公告ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateAnnouncement(id, updateData) {
    const dataToUpdate = {
      ...updateData,
      updatedAt: new Date()
    }
    return await this.updateById(id, dataToUpdate)
  }

  /**
   * 删除公告
   * @param {string} id - 公告ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteAnnouncement(id) {
    return await this.deleteById(id)
  }

  /**
   * 发布公告
   * @param {string} id - 公告ID
   * @returns {Promise<Object>} 更新结果
   */
  async publishAnnouncement(id) {
    return await this.updateById(id, {
      status: 'published',
      publishTime: new Date(),
      updatedAt: new Date()
    })
  }

  /**
   * 撤回公告
   * @param {string} id - 公告ID
   * @returns {Promise<Object>} 更新结果
   */
  async unpublishAnnouncement(id) {
    return await this.updateById(id, {
      status: 'draft',
      updatedAt: new Date()
    })
  }

  /**
   * 切换置顶状态
   * @param {string} id - 公告ID
   * @param {boolean} isSticky - 是否置顶
   * @returns {Promise<Object>} 更新结果
   */
  async toggleSticky(id, isSticky) {
    return await this.updateById(id, {
      isSticky,
      updatedAt: new Date()
    })
  }

  /**
   * 获取公告统计信息
   * @returns {Promise<Object>} 统计结果
   */
  async getAnnouncementStats() {
    try {
      // 总数统计
      const totalResult = await this.count()
      
      // 已发布数量
      const publishedResult = await this.count({ status: 'published' })
      
      // 草稿数量
      const draftResult = await this.count({ status: 'draft' })
      
      // 置顶数量
      const stickyResult = await this.count({ isSticky: true, status: 'published' })

      // 今日新增（按创建时间）
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)
      
      const todayResult = await this.count({
        createdAt: {
          $gte: today,
          $lt: tomorrow
        }
      })

      return {
        success: true,
        data: {
          total: totalResult.success ? totalResult.data.total : 0,
          published: publishedResult.success ? publishedResult.data.total : 0,
          draft: draftResult.success ? draftResult.data.total : 0,
          sticky: stickyResult.success ? stickyResult.data.total : 0,
          todayNew: todayResult.success ? todayResult.data.total : 0
        }
      }
    } catch (error) {
      console.error('[AnnouncementsDB] 获取统计信息失败:', error)
      return {
        success: false,
        message: '获取统计信息失败',
        error: error.message
      }
    }
  }

  /**
   * 清理过期公告
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupExpiredAnnouncements() {
    try {
      const now = new Date()
      const result = await this.update(
        {
          expiryTime: { $lt: now },
          status: 'published'
        },
        {
          status: 'archived',
          updatedAt: now
        }
      )

      return {
        success: true,
        data: {
          modifiedCount: result.success ? result.data.modifiedCount : 0
        },
        message: '过期公告清理完成'
      }
    } catch (error) {
      console.error('[AnnouncementsDB] 清理过期公告失败:', error)
      return {
        success: false,
        message: '清理过期公告失败',
        error: error.message
      }
    }
  }

  /**
   * 确保集合存在
   * @returns {Promise<Object>} 操作结果
   */
  async ensureCollection() {
    try {
      // 尝试创建一个测试文档然后删除，以确保集合存在
      const testDoc = await this.create({
        title: 'test',
        content: 'test',
        type: 'test',
        status: 'draft'
      })

      if (testDoc.success) {
        await this.deleteById(testDoc.data._id)
      }

      return {
        success: true,
        message: '公告集合已确保存在'
      }
    } catch (error) {
      console.error('[AnnouncementsDB] 确保集合存在失败:', error)
      return {
        success: false,
        message: '确保集合存在失败',
        error: error.message
      }
    }
  }
}

module.exports = new AnnouncementsDB()
