# 公告数据库管理指南

## 数据库结构

### announcements 集合

```javascript
{
  _id: ObjectId,              // 自动生成的ID
  title: String,              // 公告标题
  content: String,            // Markdown格式内容
  type: String,               // 类型：'announcement', 'update', 'notice', 'maintenance'
  isSticky: Boolean,          // 是否置顶
  status: String,             // 状态：'draft', 'published', 'archived'
  publishTime: Date,          // 发布时间
  expiryTime: Date,           // 过期时间（可选，null表示不过期）
  createdAt: Date,            // 创建时间
  updatedAt: Date             // 更新时间
}
```

## 创建公告示例

### 1. 欢迎公告（置顶）

```javascript
db.announcements.add({
  title: "🎉 欢迎使用时间跟踪器！",
  content: `# 欢迎使用时间跟踪器！

感谢您选择我们的时间管理工具。

## 主要功能

- **时间追踪**：精确记录工作时间
- **收入统计**：自动计算收入
- **数据同步**：云端安全存储

### 快速开始

1. 在"履历"页面添加工作信息
2. 在主页记录时间段
3. 查看"统计"了解收入情况

> 💡 **提示**：建议每天定时记录，养成良好习惯。

---

祝您使用愉快！`,
  type: "announcement",
  isSticky: true,
  status: "published",
  publishTime: new Date(),
  expiryTime: null,
  createdAt: new Date(),
  updatedAt: new Date()
})
```

### 2. 功能更新公告

```javascript
db.announcements.add({
  title: "📱 新功能上线",
  content: `# 新功能上线

## 本次更新内容

### 新增功能
- 摸鱼状态记录
- 实时在线人数显示
- 数据导出功能

### 优化改进
- 提升页面加载速度
- 优化用户界面设计
- 修复已知问题

## 使用说明

1. 点击主页的 **摸鱼** 按钮开始记录
2. 在状态页面查看当前摸鱼人数
3. 统计页面可查看摸鱼时长

*更多功能正在开发中，敬请期待！*`,
  type: "update",
  isSticky: false,
  status: "published",
  publishTime: new Date(),
  expiryTime: null,
  createdAt: new Date(),
  updatedAt: new Date()
})
```

### 3. 维护通知（有过期时间）

```javascript
db.announcements.add({
  title: "🔧 系统维护通知",
  content: `# 系统维护通知

## 维护时间
**2024年1月20日 02:00 - 04:00**

## 维护内容

### 数据库优化
- 索引重建
- 性能调优
- 数据清理

### 功能升级
- 新增备份机制
- 增强安全性
- 修复bug

## 注意事项

> ⚠️ **重要提醒**：维护期间无法使用以下功能：
> - 数据同步
> - 在线统计
> - 实时更新

---

感谢您的理解与配合！`,
  type: "maintenance",
  isSticky: false,
  status: "published",
  publishTime: new Date(),
  expiryTime: new Date("2024-01-21T00:00:00Z"), // 维护结束后过期
  createdAt: new Date(),
  updatedAt: new Date()
})
```

## 常用操作

### 查询所有已发布的公告

```javascript
db.announcements.where({
  status: 'published'
}).orderBy('publishTime', 'desc').get()
```

### 查询置顶公告

```javascript
db.announcements.where({
  status: 'published',
  isSticky: true
}).get()
```

### 查询特定类型的公告

```javascript
db.announcements.where({
  status: 'published',
  type: 'announcement'
}).get()
```

### 更新公告状态

```javascript
// 发布草稿
db.announcements.doc('公告ID').update({
  status: 'published',
  publishTime: new Date(),
  updatedAt: new Date()
})

// 撤回公告
db.announcements.doc('公告ID').update({
  status: 'draft',
  updatedAt: new Date()
})
```

### 设置/取消置顶

```javascript
// 设置置顶
db.announcements.doc('公告ID').update({
  isSticky: true,
  updatedAt: new Date()
})

// 取消置顶
db.announcements.doc('公告ID').update({
  isSticky: false,
  updatedAt: new Date()
})
```

### 删除公告

```javascript
db.announcements.doc('公告ID').remove()
```

## 数据类型说明

### type 字段值
- `announcement` - 📢 公告
- `update` - 🔄 更新
- `notice` - 📋 通知
- `maintenance` - 🔧 维护

### status 字段值
- `draft` - 草稿（测试用户可见）
- `published` - 已发布（所有用户可见）
- `archived` - 已归档（测试用户可见）

### isSticky 字段
- `true` - 置顶显示
- `false` - 正常排序

### expiryTime 字段
- `null` - 永不过期
- `Date` - 指定过期时间，过期后不会在前端显示

## 注意事项

1. **Markdown格式**：content字段使用Markdown格式，支持标题、列表、引用、代码块等
2. **时间字段**：publishTime、expiryTime、createdAt、updatedAt都使用Date类型
3. **过期处理**：系统会自动过滤已过期的公告
4. **排序规则**：置顶公告优先，然后按发布时间倒序
5. **内容安全**：确保Markdown内容安全，避免恶意代码

## 批量操作示例

### 批量发布草稿

```javascript
db.announcements.where({
  status: 'draft'
}).update({
  status: 'published',
  publishTime: new Date(),
  updatedAt: new Date()
})
```

### 清理过期公告

```javascript
db.announcements.where({
  expiryTime: db.command.lt(new Date()),
  status: 'published'
}).update({
  status: 'archived',
  updatedAt: new Date()
})
```
