<!-- 公告中心页面 -->
<view class="announcements-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">公告中心</text>
      <text class="header-subtitle">查看最新公告和更新信息</text>
    </view>
    <view class="header-icon">📢</view>
  </view>

  <!-- 类型筛选标签 -->
  <scroll-view class="type-filter" scroll-x wx:if="{{showTypeFilter}}">
    <view class="filter-tab {{currentType === 'all' ? 'active' : ''}}" 
          bindtap="switchType" data-type="all">
      全部
    </view>
    <view class="filter-tab {{currentType === 'announcement' ? 'active' : ''}}" 
          bindtap="switchType" data-type="announcement">
      📢 公告
    </view>
    <view class="filter-tab {{currentType === 'update' ? 'active' : ''}}" 
          bindtap="switchType" data-type="update">
      🔄 更新
    </view>
    <view class="filter-tab {{currentType === 'notice' ? 'active' : ''}}" 
          bindtap="switchType" data-type="notice">
      📋 通知
    </view>
    <view class="filter-tab {{currentType === 'maintenance' ? 'active' : ''}}" 
          bindtap="switchType" data-type="maintenance">
      🔧 维护
    </view>
  </scroll-view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && announcements.length === 0}}">
    <view class="loading-icon">⏳</view>
    <text class="loading-text">正在加载公告...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{errorMessage && announcements.length === 0}}">
    <view class="error-icon">❌</view>
    <text class="error-text">{{errorMessage}}</text>
    <button class="retry-button" bindtap="onRetry">重试</button>
  </view>

  <!-- 公告列表 -->
  <view class="announcements-list" wx:if="{{announcements.length > 0}}">
    <view class="announcement-card {{item.isSticky ? 'sticky' : ''}}" 
          wx:for="{{announcements}}" wx:key="_id">
      
      <!-- 置顶标识 -->
      <view class="sticky-badge" wx:if="{{item.isSticky}}">📌</view>

      <!-- 公告头部 -->
      <view class="card-header">
        <view class="title-section">
          <text class="title">{{item.title}}</text>
          <view class="type-badge {{item.type}}">
            <text class="badge-text">{{item.typeText}}</text>
          </view>
        </view>
        <text class="publish-time">{{item.formattedTime}}</text>
      </view>
      
      <!-- 公告内容 -->
      <view class="card-content">
        <view class="markdown-content">
          <block wx:for="{{item.parsedContent}}" wx:key="index" wx:for-item="block">

            <!-- 标题 -->
            <view wx:if="{{block.type === 'header'}}" class="md-header md-header-{{block.level}}">
              {{block.text}}
            </view>

            <!-- 段落 -->
            <view wx:elif="{{block.type === 'paragraph'}}" class="md-paragraph">
              <block wx:for="{{block.text}}" wx:key="index" wx:for-item="span">
                <text wx:if="{{span.type === 'text'}}" class="md-text">{{span.text}}</text>
                <text wx:elif="{{span.type === 'bold'}}" class="md-bold">{{span.text}}</text>
                <text wx:elif="{{span.type === 'italic'}}" class="md-italic">{{span.text}}</text>
                <text wx:elif="{{span.type === 'inline-code'}}" class="md-inline-code">{{span.text}}</text>
                <text wx:elif="{{span.type === 'link'}}" class="md-link" data-url="{{span.url}}" bindtap="onLinkTap">{{span.text}}</text>
                <image wx:elif="{{span.type === 'image'}}" class="md-inline-image" src="{{span.url}}" mode="widthFix" lazy-load="{{true}}" show-menu-by-longpress="{{true}}" />
              </block>
            </view>

            <!-- 图片 -->
            <view wx:elif="{{block.type === 'image'}}" class="md-image-container">
              <image class="md-image" src="{{block.url}}" mode="widthFix" lazy-load="{{true}}" show-menu-by-longpress="{{true}}" />
              <view wx:if="{{block.alt}}" class="md-image-caption">{{block.alt}}</view>
            </view>

            <!-- 列表 -->
            <view wx:elif="{{block.type === 'list'}}" class="md-list md-list-{{block.listType}}">
              <view wx:for="{{block.items}}" wx:key="index" wx:for-item="listItem" class="md-list-item">
                <view class="md-list-bullet">{{block.listType === 'ordered' ? (index + 1) + '.' : '•'}}</view>
                <view class="md-list-content">
                  <block wx:for="{{listItem.text}}" wx:key="index" wx:for-item="span">
                    <text wx:if="{{span.type === 'text'}}" class="md-text">{{span.text}}</text>
                    <text wx:elif="{{span.type === 'bold'}}" class="md-bold">{{span.text}}</text>
                    <text wx:elif="{{span.type === 'italic'}}" class="md-italic">{{span.text}}</text>
                    <text wx:elif="{{span.type === 'inline-code'}}" class="md-inline-code">{{span.text}}</text>
                    <text wx:elif="{{span.type === 'link'}}" class="md-link" data-url="{{span.url}}" bindtap="onLinkTap">{{span.text}}</text>
                    <image wx:elif="{{span.type === 'image'}}" class="md-inline-image" src="{{span.url}}" mode="widthFix" lazy-load="{{true}}" show-menu-by-longpress="{{true}}" />
                  </block>
                </view>
              </view>
            </view>

            <!-- 引用 -->
            <view wx:elif="{{block.type === 'quote'}}" class="md-quote">
              <view class="md-quote-border"></view>
              <view class="md-quote-content">
                <block wx:for="{{block.text}}" wx:key="index" wx:for-item="span">
                  <text wx:if="{{span.type === 'text'}}" class="md-text">{{span.text}}</text>
                  <text wx:elif="{{span.type === 'bold'}}" class="md-bold">{{span.text}}</text>
                  <text wx:elif="{{span.type === 'italic'}}" class="md-italic">{{span.text}}</text>
                  <text wx:elif="{{span.type === 'inline-code'}}" class="md-inline-code">{{span.text}}</text>
                  <text wx:elif="{{span.type === 'link'}}" class="md-link" data-url="{{span.url}}" bindtap="onLinkTap">{{span.text}}</text>
                  <image wx:elif="{{span.type === 'image'}}" class="md-inline-image" src="{{span.url}}" mode="widthFix" lazy-load="{{true}}" show-menu-by-longpress="{{true}}" />
                </block>
              </view>
            </view>

            <!-- 代码块 -->
            <view wx:elif="{{block.type === 'code'}}" class="md-code-block">
              <view wx:if="{{block.language}}" class="md-code-language">{{block.language}}</view>
              <view class="md-code-content">{{block.text}}</view>
            </view>

            <!-- 分割线 -->
            <view wx:elif="{{block.type === 'divider'}}" class="md-divider"></view>

          </block>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && announcements.length === 0 && !errorMessage}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">暂无公告</text>
    <text class="empty-desc">还没有发布任何公告</text>
  </view>

  <!-- 加载更多状态 -->
  <view class="load-more-container" wx:if="{{announcements.length > 0}}">
    <view class="loading-more" wx:if="{{loading}}">
      <view class="loading-more-icon">⏳</view>
      <text class="loading-more-text">正在加载更多...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && !loading}}">
      <view class="no-more-line"></view>
      <text class="no-more-text">没有更多内容了</text>
      <view class="no-more-line"></view>
    </view>
  </view>
</view>
